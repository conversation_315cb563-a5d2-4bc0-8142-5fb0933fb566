from flask import Flask, request, jsonify, render_template, send_from_directory
import requests
import openai
from dotenv import load_dotenv
import os
import json

# Load environment variables
load_dotenv()

# 配置 OpenAI API 和 DeepSeek API
api_key = os.getenv("API_KEY")
base_url = os.getenv("BASE_URL")

# 设置Google Custom Search API
API_KEY = os.getenv('GOOGLE_API_KEY')
CX = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
url = 'https://www.googleapis.com/customsearch/v1'

# 设置 Ollama 客户端
client = openai.OpenAI(api_key=api_key, base_url=base_url)

app = Flask(__name__, static_folder=".", static_url_path="")

class GoogleAgent:
    def __init__(self):
        self.client = client
        self.system_prompt = "你是一个智能助手，能够提供谷歌信息查询服务。请用户提供要求，查询并返回用户所需信息。"
        self.messages = [{"role": "system", "content": self.system_prompt}]

    def get_search_results(self, query):
        """google查询（带错误处理）"""
        params = {
            'key': API_KEY,
            'q': query,
            'cx': CX
        }

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()

            results = response.json()
            return results
        except requests.exceptions.RequestException as e:
            return f"网络请求失败：{str(e)}"
        except KeyError as e:
            return f"数据解析错误：缺少字段 {str(e)}"

    def get_response(self, user_input):
        """改进后的响应逻辑"""
        self.messages.append({"role": "user", "content": user_input})
        results = self.get_search_results(user_input)

        # 如果结果是错误消息字符串，直接返回
        if isinstance(results, str) and results.startswith("网络请求失败"):
            return results

        try:
            # 通过AI识别results（更精准）
            prompt = f"""
            用户问：{user_input}
            谷歌返回的内容（json)：{results}
            请根据用户的问题，整理谷歌返回的内容，给出用户需要的结果。
            注意：返回结果不要以json的形式返回，最好以表格的形式返回，如果不能以表格的形式返回，请以文本的形式返回。
            如果无法识别，则返回：AI无法识别谷歌提供的内容。
            """

            # 调用Ollama模型提取回答内容
            ollama_data = {
                "model": "deepseek-r1:7b",
                "prompt": prompt,
                "stream": False
            }

            try:
                ollama_response = requests.post("http://localhost:11434/api/generate", json=ollama_data)
                if ollama_response.status_code == 200:
                    ai_output = ollama_response.json().get('response', '')
                else:
                    ai_output = f"Ollama API错误: {ollama_response.status_code}"
            except Exception as e:
                ai_output = f"Ollama连接错误: {str(e)}"

            # 返回AI回答
            if ai_output:
                return ai_output
            else:
                # 无法识别
                return "无法识别谷歌提供的内容"

        except Exception as e:
            print(f"处理请求时出错: {str(e)}")  # 调试输出
            return f"处理请求时出错: {str(e)}"

# 创建GoogleAgent实例
agent = GoogleAgent()

@app.route('/')
def index():
    return send_from_directory('.', 'goosearch_app.html')

@app.route('/search', methods=['POST'])
def search():
    data = request.json
    query = data.get('query', '')

    if not query:
        return jsonify({"error": "查询内容不能为空"}), 400

    response = agent.get_response(query)
    return jsonify({"response": response})

if __name__ == '__main__':
    app.run(debug=True, port=5000)
