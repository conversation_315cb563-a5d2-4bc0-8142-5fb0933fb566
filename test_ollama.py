import requests
import json

# 测试Ollama原生API
def test_ollama_native():
    url = "http://localhost:11434/api/generate"
    data = {
        "model": "deepseek-r1:7b",
        "prompt": "Hello, how are you?",
        "stream": False
    }

    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("Success!")
            print(result.get('response', 'No response'))
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

# 测试OpenAI兼容API
def test_openai_compatible():
    import openai
    from dotenv import load_dotenv
    import os

    load_dotenv()

    client = openai.OpenAI(
        api_key="ollama",
        base_url="http://localhost:11434/v1"
    )

    try:
        response = client.chat.completions.create(
            model="deepseek-r1:7b",
            messages=[{"role": "user", "content": "Hello, how are you?"}],
            temperature=0.3,
            max_tokens=100
        )
        print("OpenAI Compatible API Success!")
        print(response.choices[0].message.content)
    except Exception as e:
        print(f"OpenAI Compatible API Error: {e}")

print("Testing Ollama Native API:")
test_ollama_native()

print("\nTesting OpenAI Compatible API:")
test_openai_compatible()
