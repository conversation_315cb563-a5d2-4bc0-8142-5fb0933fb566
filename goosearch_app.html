<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GooSearch - AI 增强的谷歌搜索</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .search-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .search-title {
            text-align: center;
            margin-bottom: 30px;
            color: #4285F4;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .result-container {
            margin-top: 30px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .loading {
            text-align: center;
            display: none;
            margin: 20px 0;
        }
        .loading img {
            width: 50px;
            height: 50px;
        }
        .error-message {
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1rem;
        }
        .markdown-body th, .markdown-body td {
            border: 1px solid #dee2e6;
            padding: 8px;
        }
        .markdown-body th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-container">
            <h1 class="search-title">GooSearch</h1>
            <p class="text-center text-muted mb-4">AI 增强的谷歌搜索引擎</p>

            <div class="search-box">
                <div class="input-group mb-3">
                    <input type="text" id="search-input" class="form-control" placeholder="输入您的搜索内容..." aria-label="Search">
                    <button class="btn btn-primary" type="button" id="search-button">搜索</button>
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在搜索并分析结果，请稍候...</p>
            </div>

            <div class="error-message" id="error-message"></div>

            <div class="result-container markdown-body" id="result-container">
                <p class="text-center text-muted">搜索结果将在这里显示</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const searchButton = document.getElementById('search-button');
            const resultContainer = document.getElementById('result-container');
            const loadingElement = document.getElementById('loading');
            const errorElement = document.getElementById('error-message');

            // 回车键搜索
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // 点击搜索按钮
            searchButton.addEventListener('click', performSearch);

            function performSearch() {
                const query = searchInput.value.trim();

                if (!query) {
                    showError('请输入搜索内容');
                    return;
                }

                // 显示加载状态
                loadingElement.style.display = 'block';
                errorElement.style.display = 'none';
                resultContainer.innerHTML = '';

                // 发送请求到后端
                fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('搜索请求失败');
                    }
                    return response.json();
                })
                .then(data => {
                    loadingElement.style.display = 'none';

                    if (data.error) {
                        showError(data.error);
                        return;
                    }

                    // 将响应内容转换为HTML并显示
                    const responseHtml = marked.parse(data.response);
                    resultContainer.innerHTML = responseHtml;
                })
                .catch(error => {
                    loadingElement.style.display = 'none';
                    showError(error.message || '搜索过程中出现错误');
                    console.error('Search error:', error);
                });
            }

            function showError(message) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        });
    </script>
</body>
</html>