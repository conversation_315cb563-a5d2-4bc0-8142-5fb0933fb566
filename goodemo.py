import re
import requests
import pandas as pd
from dotenv import load_dotenv
import os
import openai
import json

# Load environment variables
load_dotenv()

# 配置 OpenAI API 和 DeepSeek API
api_key = os.getenv("API_KEY")
base_url = os.getenv("BASE_URL")

# 设置Google Custom Search API
API_KEY = os.getenv('GOOGLE_API_KEY')
CX = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
url='https://www.googleapis.com/customsearch/v1'

# 设置 DeepSeek 客户端
client = openai.OpenAI(api_key=api_key, base_url=base_url)

class GoogleAgent:
    def __init__(self):
        self.client = client
        self.system_prompt = "你是一个智能助手，能够提供谷歌信息查询服务。请用户提供要求，查询并返回用户所需信息。"
        self.messages = [{"role": "system", "content": self.system_prompt}]
    
    def get_search_results(self, query):
        """google查询（带错误处理）"""
        params = {
            'key': API_KEY,
            'q':query,
            'cx':CX
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            results = response.json()
            if results.get('status') != '1':
                return f"请求失败：{results}"
            return results
        except requests.exceptions.RequestException as e:
            return f"网络请求失败：{str(e)}"
        except KeyError as e:
            return f"数据解析错误：缺少字段 {str(e)}"
    
    def get_response(self, user_input):
        """改进后的响应逻辑"""
        self.messages.append({"role": "user", "content": user_input})
        results = self.get_search_results(user_input)
        #print(f'这是google的结果：{results}')
        try:
            # 通过AI识别results（更精准）
            prompt = f"""
            用户问：{user_input}
            谷歌返回的内容（json)：{results}
            请根据用户的问题，整理谷歌返回的内容，给出用户需要的结果。
            注意：返回结果不要以json的形式返回，最好以表格的形式返回，如果不能以表格的形式返回，请以文本的形式返回。
            如果无法识别，则返回：AI无法识别谷歌提供的内容。
            """
            
            # 调用模型提取回答内容
            extraction_response = self.client.chat.completions.create(
                model="deepseek/deepseek-chat-v3-0324:free",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=2000
            )
            
            # 解析AI回答
            try:
                ai_output = extraction_response.choices[0].message.content
            except Exception as e:
                print(f"解析AI回答失败: {str(e)}")  # 调试输出
                ai_output = None
            
            if ai_output:
                return ai_output
            else:
                # 无法识别
                print(f"无法识别谷歌提供的内容: {results}")  # 调试输出
                return ai_output

        except Exception as e:
            print(f"处理请求时出错: {str(e)}")  # 调试输出
            return f"处理请求时出错: {str(e)}"

if __name__ == "__main__":
    agent = GoogleAgent()
    while True:
        user_input = input("You: ")
        if user_input.lower() in ["exit", "quit"]:
            break
        response = agent.get_response(user_input)
        print(f"Assistant: {response}")