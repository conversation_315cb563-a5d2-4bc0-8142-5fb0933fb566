import requests
import json
import time

def test_ollama_detailed():
    url = "http://localhost:11434/api/chat"
    data = {
        "model": "deepseek-r1:7b",
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "stream": False
    }
    
    print(f"Testing URL: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    print("=" * 50)
    
    try:
        print("Sending request...")
        response = requests.post(url, json=data, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text Length: {len(response.text)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("Success! Response JSON:")
                print(json.dumps(result, indent=2))
                
                # 提取消息内容
                message_content = result.get('message', {}).get('content', '')
                print(f"\nExtracted content: {message_content}")
                
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
        else:
            print(f"Error response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("Request timed out")
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_ollama_detailed()
